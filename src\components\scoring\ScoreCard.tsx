import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Stack,
  useTheme,
  Tooltip
} from '@mui/material';
import { alpha } from '@mui/material/styles';
import {
  TrendingUp,
  TrendingDown,
  TrendingFlat,
  Psychology,
  Shield,
  Timeline,
  Rule
} from '@mui/icons-material';
import { ScoreMetrics } from '../../types/score';

interface ScoreCardProps {
  score: ScoreMetrics;
  trend: 'improving' | 'declining' | 'stable';
  period: 'daily' | 'weekly' | 'monthly';
  compact?: boolean;
}

const ScoreCard: React.FC<ScoreCardProps> = ({ 
  score, 
  trend, 
  period, 
  compact = false 
}) => {
  const theme = useTheme();

  const getScoreColor = (value: number) => {
    if (value >= 80) return theme.palette.success.main;
    if (value >= 60) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'improving':
        return <TrendingUp sx={{ color: theme.palette.success.main }} />;
      case 'declining':
        return <TrendingDown sx={{ color: theme.palette.error.main }} />;
      default:
        return <TrendingFlat sx={{ color: theme.palette.text.secondary }} />;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'improving':
        return theme.palette.success.main;
      case 'declining':
        return theme.palette.error.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const scoreComponents = [
    {
      name: 'Consistency',
      value: score.consistency,
      icon: <Rule />,
      description: 'How well you stick to your trading patterns'
    },
    {
      name: 'Risk Mgmt',
      value: score.riskManagement,
      icon: <Shield />,
      description: 'Risk management and position sizing discipline'
    },
    {
      name: 'Performance',
      value: score.performance,
      icon: <Timeline />,
      description: 'Performance consistency vs historical patterns'
    },
    {
      name: 'Discipline',
      value: score.discipline,
      icon: <Psychology />,
      description: 'Trading discipline and emotional control'
    }
  ];

  if (compact) {
    return (
      <Card
        sx={{
          minWidth: 200,
          backgroundColor: theme.palette.mode === 'dark'
            ? alpha(theme.palette.background.paper, 0.8)
            : theme.palette.background.paper,
          borderRadius: 2,
          boxShadow: theme.shadows[2],
          border: `1px solid ${theme.palette.mode === 'dark'
            ? alpha(theme.palette.common.white, 0.1)
            : alpha(theme.palette.common.black, 0.1)}`,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[4],
          }
        }}
      >
        <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
            <Typography
              variant="subtitle2"
              sx={{
                color: theme.palette.text.secondary,
                fontWeight: 500
              }}
            >
              {period.charAt(0).toUpperCase() + period.slice(1)} Score
            </Typography>
            {getTrendIcon()}
          </Stack>

          <Typography
            variant="h4"
            sx={{
              color: getScoreColor(score.overall),
              fontWeight: 'bold',
              mb: 1
            }}
          >
            {score.overall.toFixed(0)}%
          </Typography>

          <LinearProgress
            variant="determinate"
            value={score.overall}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: theme.palette.mode === 'dark'
                ? alpha(theme.palette.common.white, 0.1)
                : theme.palette.grey[200],
              '& .MuiLinearProgress-bar': {
                backgroundColor: getScoreColor(score.overall),
                borderRadius: 3
              }
            }}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      sx={{
        backgroundColor: theme.palette.mode === 'dark'
          ? alpha(theme.palette.background.paper, 0.8)
          : theme.palette.background.paper,
        borderRadius: 2,
        boxShadow: theme.shadows[2],
        border: `1px solid ${theme.palette.mode === 'dark'
          ? alpha(theme.palette.common.white, 0.1)
          : alpha(theme.palette.common.black, 0.1)}`,
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
        }
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography
            variant="h6"
            sx={{
              color: theme.palette.text.primary,
              fontWeight: 600
            }}
          >
            📊 {period.charAt(0).toUpperCase() + period.slice(1)} Trading Score
          </Typography>
          <Chip
            icon={getTrendIcon()}
            label={trend.charAt(0).toUpperCase() + trend.slice(1)}
            size="small"
            sx={{
              backgroundColor: alpha(getTrendColor(), 0.1),
              color: getTrendColor(),
              fontWeight: 500,
              border: `1px solid ${alpha(getTrendColor(), 0.3)}`,
              '& .MuiChip-icon': {
                color: getTrendColor()
              }
            }}
          />
        </Stack>

        {/* Overall Score */}
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Typography 
            variant="h2" 
            sx={{ 
              color: getScoreColor(score.overall),
              fontWeight: 'bold',
              mb: 1
            }}
          >
            {score.overall.toFixed(0)}%
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Overall Trading Score
          </Typography>
          <LinearProgress
            variant="determinate"
            value={score.overall}
            sx={{
              height: 8,
              borderRadius: 4,
              mt: 1,
              backgroundColor: theme.palette.mode === 'dark'
                ? alpha(theme.palette.common.white, 0.1)
                : theme.palette.grey[200],
              '& .MuiLinearProgress-bar': {
                backgroundColor: getScoreColor(score.overall),
                borderRadius: 4
              }
            }}
          />
        </Box>

        {/* Component Scores */}
        <Stack spacing={2}>
          {scoreComponents.map((component) => (
            <Tooltip key={component.name} title={component.description} arrow>
              <Box>
                <Stack direction="row" alignItems="center" justifyContent="space-between" mb={0.5}>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Box sx={{ color: theme.palette.text.secondary }}>
                      {component.icon}
                    </Box>
                    <Typography variant="body2" fontWeight="medium">
                      {component.name}
                    </Typography>
                  </Stack>
                  <Typography 
                    variant="body2" 
                    fontWeight="bold"
                    sx={{ color: getScoreColor(component.value) }}
                  >
                    {component.value.toFixed(0)}%
                  </Typography>
                </Stack>
                <LinearProgress
                  variant="determinate"
                  value={component.value}
                  sx={{
                    height: 4,
                    borderRadius: 2,
                    backgroundColor: theme.palette.mode === 'dark'
                      ? alpha(theme.palette.common.white, 0.1)
                      : theme.palette.grey[200],
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getScoreColor(component.value),
                      borderRadius: 2
                    }
                  }}
                />
              </Box>
            </Tooltip>
          ))}
        </Stack>
      </CardContent>
    </Card>
  );
};

export default ScoreCard;
